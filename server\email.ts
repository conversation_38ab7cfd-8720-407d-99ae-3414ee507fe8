import nodemailer from 'nodemailer';
import type { Lead } from '@shared/schema';

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

export class EmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;
  private toEmail: string;

  constructor(config: EmailConfig, fromEmail: string, toEmail: string) {
    this.fromEmail = fromEmail;
    this.toEmail = toEmail;
    
    this.transporter = nodemailer.createTransport({
      host: config.host,
      port: config.port,
      secure: config.secure,
      auth: config.auth,
    });
  }

  async sendLeadNotification(lead: Lead): Promise<void> {
    const subject = `New Lead Submission from ${lead.name}`;
    
    const htmlContent = this.generateLeadEmailHTML(lead);
    const textContent = this.generateLeadEmailText(lead);

    const mailOptions = {
      from: this.fromEmail,
      to: this.toEmail,
      subject,
      text: textContent,
      html: htmlContent,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`Lead notification email sent for: ${lead.email}`);
    } catch (error) {
      console.error('Failed to send lead notification email:', error);
      throw error;
    }
  }

  async sendConfirmationEmail(lead: Lead): Promise<void> {
    const subject = 'Thank you for your interest in SiteNevada!';
    
    const htmlContent = this.generateConfirmationEmailHTML(lead);
    const textContent = this.generateConfirmationEmailText(lead);

    const mailOptions = {
      from: this.fromEmail,
      to: lead.email,
      subject,
      text: textContent,
      html: htmlContent,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log(`Confirmation email sent to: ${lead.email}`);
    } catch (error) {
      console.error('Failed to send confirmation email:', error);
      throw error;
    }
  }

  private generateLeadEmailHTML(lead: Lead): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>New Lead Submission</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1e40af; color: white; padding: 20px; text-align: center; }
          .content { background-color: #f8fafc; padding: 20px; }
          .field { margin-bottom: 15px; }
          .label { font-weight: bold; color: #374151; }
          .value { margin-top: 5px; padding: 10px; background-color: white; border-radius: 4px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>New Lead Submission</h1>
          </div>
          <div class="content">
            <div class="field">
              <div class="label">Name:</div>
              <div class="value">${lead.name}</div>
            </div>
            <div class="field">
              <div class="label">Business Name:</div>
              <div class="value">${lead.businessName}</div>
            </div>
            <div class="field">
              <div class="label">Email:</div>
              <div class="value">${lead.email}</div>
            </div>
            ${lead.currentSite ? `
            <div class="field">
              <div class="label">Current Website:</div>
              <div class="value"><a href="${lead.currentSite}" target="_blank">${lead.currentSite}</a></div>
            </div>
            ` : ''}
            ${lead.goals ? `
            <div class="field">
              <div class="label">Goals/Needs:</div>
              <div class="value">${lead.goals}</div>
            </div>
            ` : ''}
            <div class="field">
              <div class="label">Submitted:</div>
              <div class="value">${lead.createdAt.toLocaleString()}</div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateLeadEmailText(lead: Lead): string {
    return `
New Lead Submission

Name: ${lead.name}
Business Name: ${lead.businessName}
Email: ${lead.email}
${lead.currentSite ? `Current Website: ${lead.currentSite}` : ''}
${lead.goals ? `Goals/Needs: ${lead.goals}` : ''}
Submitted: ${lead.createdAt.toLocaleString()}
    `.trim();
  }

  private generateConfirmationEmailHTML(lead: Lead): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Thank you for your interest!</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #1e40af; color: white; padding: 20px; text-align: center; }
          .content { background-color: #f8fafc; padding: 20px; }
          .highlight { background-color: #dbeafe; padding: 15px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Thank You, ${lead.name}!</h1>
          </div>
          <div class="content">
            <p>Thank you for your interest in SiteNevada! We've received your request for a free website preview.</p>
            
            <div class="highlight">
              <h3>What happens next?</h3>
              <ul>
                <li>We'll review your business needs and goals</li>
                <li>Our team will reach out within 24 hours</li>
                <li>We'll schedule a brief consultation to discuss your vision</li>
                <li>You'll receive your custom website preview</li>
              </ul>
            </div>

            <p>We're excited to help ${lead.businessName} establish a strong online presence!</p>
            
            <p>If you have any immediate questions, feel free to reply to this email or contact <NAME_EMAIL>.</p>
            
            <p>Best regards,<br>
            The SiteNevada Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateConfirmationEmailText(lead: Lead): string {
    return `
Thank You, ${lead.name}!

Thank you for your interest in SiteNevada! We've received your request for a free website preview.

What happens next?
- We'll review your business needs and goals
- Our team will reach out within 24 hours
- We'll schedule a brief consultation to discuss your vision
- You'll receive your custom website preview

We're excited to help ${lead.businessName} establish a strong online presence!

If you have any immediate questions, feel free to reply to this email or contact <NAME_EMAIL>.

Best regards,
The SiteNevada Team
    `.trim();
  }

  async testConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      console.log('Email service connection verified successfully');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}

// Factory function to create email service from environment variables
export function createEmailService(): EmailService | null {
  const {
    SMTP_HOST,
    SMTP_PORT,
    SMTP_SECURE,
    SMTP_USER,
    SMTP_PASS,
    FROM_EMAIL,
    TO_EMAIL
  } = process.env;

  if (!SMTP_HOST || !SMTP_PORT || !SMTP_USER || !SMTP_PASS || !FROM_EMAIL || !TO_EMAIL) {
    console.warn('Email service not configured - missing required environment variables');
    return null;
  }

  const config: EmailConfig = {
    host: SMTP_HOST,
    port: parseInt(SMTP_PORT, 10),
    secure: SMTP_SECURE === 'true',
    auth: {
      user: SMTP_USER,
      pass: SMTP_PASS,
    },
  };

  return new EmailService(config, FROM_EMAIL, TO_EMAIL);
}
