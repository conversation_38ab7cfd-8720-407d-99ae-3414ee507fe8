import { Code, Server, Plus, Info } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

export function PricingSection() {
  const pricingItems = [
    {
      icon: Code,
      service: "Single Page Build",
      description: "Complete website design and development",
      price: "$400",
      period: "one-time",
      iconBg: "bg-blue-100 dark:bg-blue-900/30",
      iconColor: "text-blue-600 dark:text-blue-400",
      priceColor: "text-blue-600 dark:text-blue-400"
    },
    {
      icon: Server,
      service: "Hosting + Support Plan",
      description: "Full hosting with maintenance and support",
      price: "$50",
      period: "per month",
      iconBg: "bg-emerald-100 dark:bg-emerald-900/30",
      iconColor: "text-emerald-600 dark:text-emerald-400",
      priceColor: "text-emerald-600 dark:text-emerald-400",
      highlight: true
    },
    {
      icon: Plus,
      service: "Add-ons (optional)",
      description: "SEO, CMS, analytics, and content services",
      price: "Custom",
      period: "quoted per need",
      iconBg: "bg-purple-100 dark:bg-purple-900/30",
      iconColor: "text-purple-600 dark:text-purple-400",
      priceColor: "text-purple-600 dark:text-purple-400"
    }
  ];

  return (
    <section id="pricing" className="py-20 bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-800 dark:text-slate-50">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300">
            No hidden fees, no long-term contracts, no surprises
          </p>
        </motion.div>

        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="max-w-4xl mx-auto"
        >
          <Card className="bg-white dark:bg-slate-800 shadow-xl border border-slate-200 dark:border-slate-700 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-slate-50 dark:bg-slate-700">
                  <tr>
                    <th className="px-6 py-4 text-left text-lg font-semibold text-slate-800 dark:text-slate-50">
                      Service
                    </th>
                    <th className="px-6 py-4 text-right text-lg font-semibold text-slate-800 dark:text-slate-50">
                      Cost
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200 dark:divide-slate-600">
                  {pricingItems.map((item, index) => {
                    const IconComponent = item.icon;
                    return (
                      <motion.tr
                        key={item.service}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        className={`hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors ${
                          item.highlight ? "bg-blue-50/50 dark:bg-blue-900/10" : ""
                        }`}
                      >
                        <td className="px-6 py-6">
                          <div className="flex items-center">
                            <div className={`w-10 h-10 ${item.iconBg} rounded-lg flex items-center justify-center mr-4`}>
                              <IconComponent className={`${item.iconColor} h-5 w-5`} />
                            </div>
                            <div>
                              <div className="font-semibold text-slate-800 dark:text-slate-50">
                                {item.service}
                              </div>
                              <div className="text-sm text-slate-500 dark:text-slate-400">
                                {item.description}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-6 text-right">
                          <div className={`text-2xl font-bold ${item.priceColor}`}>
                            {item.price}
                          </div>
                          <div className="text-sm text-slate-500 dark:text-slate-400">
                            {item.period}
                          </div>
                        </td>
                      </motion.tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card>

          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-8"
          >
            <p className="text-slate-600 dark:text-slate-300 text-lg flex items-center justify-center">
              <Info className="text-blue-500 mr-2 h-5 w-5" />
              All prices include initial consultation and design revisions
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
