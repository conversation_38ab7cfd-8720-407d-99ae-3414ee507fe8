import { Mountain, Mail, MapPin } from "lucide-react";
import { FaFacebookF, FaTwitter, FaLinkedinIn } from "react-icons/fa";

export function Footer() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const offsetTop = element.offsetTop - 80;
      window.scrollTo({
        top: offsetTop,
        behavior: "smooth",
      });
    }
  };

  return (
    <footer className="bg-slate-900 dark:bg-slate-950 text-slate-300 py-16">
      <div className="container mx-auto px-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div>
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-emerald-500 rounded-lg flex items-center justify-center">
                <Mountain className="text-white text-sm" />
              </div>
              <span className="text-xl font-bold text-white">SiteNevada</span>
            </div>
            <p className="text-slate-400 leading-relaxed">
              Professional website design and hosting for Las Vegas Valley businesses. 
              Modern, mobile-ready websites with no long-term contracts.
            </p>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Services</h3>
            <ul className="space-y-2">
              <li>
                <button 
                  onClick={() => scrollToSection("services")}
                  className="text-slate-400 hover:text-blue-400 transition-colors text-left"
                >
                  Website Design
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection("services")}
                  className="text-slate-400 hover:text-blue-400 transition-colors text-left"
                >
                  Hosting & Support
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection("services")}
                  className="text-slate-400 hover:text-blue-400 transition-colors text-left"
                >
                  SEO Services
                </button>
              </li>
              <li>
                <button 
                  onClick={() => scrollToSection("demos")}
                  className="text-slate-400 hover:text-blue-400 transition-colors text-left"
                >
                  Live Demos
                </button>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Contact</h3>
            <div className="space-y-3">
              <div className="flex items-center">
                <Mail className="text-blue-400 mr-3 h-4 w-4" />
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-slate-400 hover:text-blue-400 transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <MapPin className="text-blue-400 mr-3 h-4 w-4" />
                <span className="text-slate-400">Las Vegas Valley, Nevada</span>
              </div>
            </div>

            <div className="flex space-x-4 mt-6">
              {/* Social links commented out temporarily
              <a 
                href="#" 
                className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center text-slate-400 hover:text-blue-400 hover:bg-slate-700 transition-colors"
                aria-label="Facebook"
              >
                <FaFacebookF />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center text-slate-400 hover:text-blue-400 hover:bg-slate-700 transition-colors"
                aria-label="Twitter"
              >
                <FaTwitter />
              </a>
              <a 
                href="#" 
                className="w-10 h-10 bg-slate-800 rounded-lg flex items-center justify-center text-slate-400 hover:text-blue-400 hover:bg-slate-700 transition-colors"
                aria-label="LinkedIn"
              >
                <FaLinkedinIn />
              </a>
              */}
            </div>
          </div>
        </div>

        <div className="pt-8 border-t border-slate-800">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-500 text-sm mb-4 md:mb-0">
              &copy; 2024 SiteNevada. All rights reserved.
            </p>
            <div className="flex space-x-6 text-sm">
              <a href="#" className="text-slate-500 hover:text-blue-400 transition-colors">
                Terms of Service
              </a>
              <a href="#" className="text-slate-500 hover:text-blue-400 transition-colors">
                Privacy Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

