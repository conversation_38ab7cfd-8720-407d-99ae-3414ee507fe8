import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertLeadSchema } from "@shared/schema";
import { z } from "zod";
import { createEmailService } from "./email";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize email service
  const emailService = createEmailService();

  // Contact form submission
  app.post("/api/leads", async (req, res) => {
    try {
      const leadData = insertLeadSchema.parse(req.body);
      const lead = await storage.createLead(leadData);

      // Send emails if email service is configured
      if (emailService) {
        try {
          // Send notification email to admin
          await emailService.sendLeadNotification(lead);

          // Send confirmation email to user
          await emailService.sendConfirmationEmail(lead);

          console.log(`Emails sent successfully for lead: ${lead.email}`);
        } catch (emailError) {
          // Log email error but don't fail the request
          console.error('Email sending failed:', emailError);
        }
      } else {
        console.log('Email service not configured - skipping email notifications');
      }

      res.json({ success: true, lead });
    } catch (error) {
      if (error instanceof z.ZodError) {
        res.status(400).json({
          success: false,
          message: "Invalid form data",
          errors: error.errors
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Failed to submit form"
        });
      }
    }
  });

  // Get all leads (optional admin endpoint)
  app.get("/api/leads", async (req, res) => {
    try {
      const leads = await storage.getLeads();
      res.json({ success: true, leads });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Failed to fetch leads"
      });
    }
  });

  // Test email configuration endpoint
  app.get("/api/test-email", async (req, res) => {
    if (!emailService) {
      return res.status(503).json({
        success: false,
        message: "Email service not configured. Please check environment variables."
      });
    }

    try {
      const isConnected = await emailService.testConnection();
      if (isConnected) {
        res.json({
          success: true,
          message: "Email service is properly configured and connected."
        });
      } else {
        res.status(500).json({
          success: false,
          message: "Email service configuration test failed."
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: "Email service test failed",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
