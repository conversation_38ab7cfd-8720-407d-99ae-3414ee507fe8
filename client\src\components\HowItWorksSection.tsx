import { Quote, <PERSON> } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

export function HowItWorksSection() {
  const steps = [
    {
      number: 1,
      title: "Submit Request",
      description: "Fill out our simple form with your business details and requirements",
      color: "bg-blue-500"
    },
    {
      number: 2,
      title: "We Design",
      description: "Our team creates a personalized landing page tailored to your business",
      color: "bg-emerald-500"
    },
    {
      number: 3,
      title: "Launch & Upgrade",
      description: "Approve your design and choose to launch or upgrade to our hosted plan",
      color: "bg-purple-500"
    }
  ];

  return (
    <section id="how-it-works" className="py-20 bg-slate-50 dark:bg-slate-900">
      <div className="container mx-auto px-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-800 dark:text-slate-50">
            How It Works
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300">
            Simple process to get your professional website live
          </p>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {steps.map((step, index) => (
              <motion.div
                key={step.number}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className={`w-16 h-16 ${step.color} rounded-full flex items-center justify-center mx-auto mb-6 text-white text-2xl font-bold`}>
                  {step.number}
                </div>
                <h3 className="text-xl font-bold mb-3 text-slate-800 dark:text-slate-50">
                  {step.title}
                </h3>
                <p className="text-slate-600 dark:text-slate-300">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <Card className="bg-gradient-to-r from-blue-50 to-emerald-50 dark:from-slate-800 dark:to-slate-700 border border-blue-200 dark:border-slate-600">
              <CardContent className="p-8">
                <div className="text-center">
                  <Quote className="text-blue-500 text-3xl mb-4 mx-auto" />
                  <blockquote className="text-xl md:text-2xl font-medium text-slate-800 dark:text-slate-50 mb-4">
                    "You own the code. You own the content. No hidden fees. No long-term contracts."
                  </blockquote>
                  <div className="flex items-center justify-center space-x-4 text-slate-600 dark:text-slate-300">
                    <Shield className="text-green-500" />
                    <span>100% Ownership Guarantee</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
