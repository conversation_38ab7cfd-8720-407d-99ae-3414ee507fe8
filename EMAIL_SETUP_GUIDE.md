# Email Setup Guide for SiteNevada

This guide will help you set up email notifications for your contact form using Gmail SMTP.

## What's Been Added

1. **Email Service** (`server/email.ts`): Handles sending emails via Gmail SMTP
2. **Updated Routes** (`server/routes.ts`): Integrated email sending into the contact form submission
3. **Environment Configuration** (`.env.example`): Documents required environment variables
4. **Test Endpoint**: `/api/test-email` to verify email configuration

## Features

When a user submits the contact form, the system will:
1. **Save the lead** to your database (as before)
2. **Send a notification email** to you with the lead details
3. **Send a confirmation email** to the user thanking them for their interest

## Gmail Setup Instructions

### Step 1: Enable 2-Factor Authentication
1. Go to your Google Account settings
2. Navigate to Security
3. Enable 2-Factor Authentication if not already enabled

### Step 2: Generate App Password
1. Go to [Google App Passwords](https://myaccount.google.com/apppasswords)
2. Select "Mail" as the app
3. Generate a new App Password
4. **Save this password** - you'll need it for the configuration

### Step 3: Configure Environment Variables

Create a `.env` file in your project root with these variables:

```env
# Gmail SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false

# Your Gmail credentials
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password

# Email addresses
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>
```

**Important Notes:**
- Use the **App Password** (16 characters) for `SMTP_PASS`, NOT your regular Gmail password
- `SMTP_USER` and `FROM_EMAIL` must be the same Gmail address
- `TO_EMAIL` is where you want to receive lead notifications

## Testing the Setup

### 1. Test Email Configuration
Visit: `http://localhost:5005/api/test-email`

This will verify your email configuration without sending actual emails.

### 2. Test Full Flow
1. Fill out the contact form on your website
2. Check that the lead is saved (visit `/api/leads`)
3. Check your email for:
   - Lead notification (sent to `TO_EMAIL`)
   - User should receive confirmation email

## Email Templates

The system sends two types of emails:

### 1. Lead Notification (to you)
- **Subject**: "New Lead Submission from [Name]"
- **Content**: All form details in a formatted layout
- **Sent to**: `TO_EMAIL` address

### 2. Confirmation Email (to user)
- **Subject**: "Thank you for your interest in SiteNevada!"
- **Content**: Professional thank you message with next steps
- **Sent to**: User's email address from the form

## Troubleshooting

### Common Issues:

1. **"Email service not configured"**
   - Check that all environment variables are set
   - Restart your server after adding environment variables

2. **"Authentication failed"**
   - Verify you're using an App Password, not your regular password
   - Ensure 2FA is enabled on your Gmail account

3. **"Connection refused"**
   - Check your internet connection
   - Verify SMTP settings (host, port)

4. **Emails not being received**
   - Check spam/junk folders
   - Verify the `TO_EMAIL` address is correct
   - Test with the `/api/test-email` endpoint first

### Debug Mode
Check your server console for email-related log messages:
- "Email service connection verified successfully"
- "Lead notification email sent for: [email]"
- "Confirmation email sent to: [email]"

## Security Notes

- Never commit your `.env` file to version control
- Use App Passwords instead of regular passwords
- Consider using environment-specific email addresses for testing vs production
- The email service gracefully handles failures - form submissions will still work even if emails fail

## Customization

You can customize the email templates by editing the methods in `server/email.ts`:
- `generateLeadEmailHTML()` - Admin notification email
- `generateConfirmationEmailHTML()` - User confirmation email

Both HTML and plain text versions are included for better email client compatibility.
