# SiteNevada - Modern Website Design for Las Vegas Businesses

## Overview

SiteNevada is a full-stack web application that provides professional website design and hosting services specifically for Las Vegas Valley businesses. The application serves as both a marketing website showcasing services and a lead generation platform with a contact form system.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with a custom design system
- **UI Components**: Radix UI primitives with shadcn/ui component library
- **State Management**: React Query (TanStack Query) for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion for smooth interactions
- **Theme**: Dark/light mode support with custom theme provider

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Language**: TypeScript with ES modules
- **Database**: Drizzle ORM configured for PostgreSQL (with @neondatabase/serverless)
- **Session Management**: Express sessions with PostgreSQL store
- **API Design**: RESTful endpoints for lead management
- **Validation**: Zod schemas shared between client and server

### Build System
- **Development**: Vite for fast development and HMR
- **Production Build**: Vite for client bundling, esbuild for server bundling
- **TypeScript**: Strict mode enabled with path aliases

## Key Components

### Database Schema (`shared/schema.ts`)
- **Users Table**: Basic user authentication (id, username, password)
- **Leads Table**: Contact form submissions (id, name, businessName, email, currentSite, goals, createdAt)
- **Validation**: Drizzle-zod integration for type-safe schema validation

### Storage Layer (`server/storage.ts`)
- **Interface**: IStorage defining data access methods
- **Implementation**: MemStorage (in-memory storage for development)
- **Methods**: User and lead CRUD operations
- **Future**: Designed to easily swap for PostgreSQL implementation

### API Endpoints (`server/routes.ts`)
- `POST /api/leads` - Submit contact form data
- `GET /api/leads` - Retrieve all leads (admin endpoint)
- Error handling with proper HTTP status codes
- Input validation using Zod schemas

### Frontend Pages
- **Landing Page**: Single-page application with multiple sections
- **Sections**: Navigation, Hero, Services, How It Works, Demos, Pricing, Contact, Footer
- **Responsive Design**: Mobile-first approach with breakpoint-based layouts

## Data Flow

1. **Lead Generation**: Users fill out contact form on landing page
2. **Validation**: Client-side validation with React Hook Form + Zod
3. **Submission**: Form data sent to `/api/leads` endpoint
4. **Processing**: Server validates and stores lead data
5. **Feedback**: Success/error messages displayed to user
6. **Storage**: Leads stored in-memory (development) or PostgreSQL (production)

## External Dependencies

### Core Dependencies
- **React Ecosystem**: React, React DOM, React Query
- **UI Framework**: Radix UI components, shadcn/ui, Tailwind CSS
- **Database**: Drizzle ORM, @neondatabase/serverless
- **Validation**: Zod for schema validation
- **Forms**: React Hook Form with resolvers
- **Animation**: Framer Motion
- **Icons**: Lucide React, React Icons

### Development Tools
- **Build Tools**: Vite, esbuild, TypeScript
- **Replit Integration**: Replit-specific plugins and error handling
- **Code Quality**: PostCSS, Autoprefixer

## Deployment Strategy

### Replit Deployment
- **Environment**: Replit with Node.js 20, PostgreSQL 16
- **Development**: `npm run dev` with hot reload on port 5005
- **Production Build**: `npm run build` creates optimized bundles
- **Production Start**: `npm run start` serves built application
- **Database**: Automatic PostgreSQL provisioning via DATABASE_URL
- **Scaling**: Autoscale deployment target configured

### Environment Configuration
- **Database**: PostgreSQL connection via DATABASE_URL environment variable
- **Sessions**: PostgreSQL-backed session storage
- **Static Assets**: Served from dist/public directory
- **API Routes**: Express server handles /api/* endpoints
- **SPA Fallback**: All other routes serve React application

### Build Process
1. **Client Build**: Vite bundles React app to dist/public
2. **Server Build**: esbuild bundles Express server to dist/index.js
3. **Asset Optimization**: CSS/JS minification and bundling
4. **Type Checking**: TypeScript compilation validation

## Changelog
- June 13, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.