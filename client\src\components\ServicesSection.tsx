import { Code, Server, Plus, Check } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";

export function ServicesSection() {
  const services = [
    {
      icon: Code,
      title: "Single Build",
      price: "$400",
      period: "one-time payment",
      features: [
        "Sleek, fast-loading pages",
        "Smooth scrolling design",
        "Mobile-optimized layout",
        "Google Maps & Reviews embedded",
        "Social media integration"
      ],
      gradient: "bg-blue-500",
      popular: false
    },
    {
      icon: Server,
      title: "Hosting & Support",
      price: "$50",
      period: "per month",
      features: [
        "Secure server hosting",
        "SSL, DNS & Domain setup",
        "Email forwarding",
        "Uptime monitoring",
        "Light maintenance (text/images)"
      ],
      gradient: "bg-gradient-to-r from-blue-500 to-emerald-500",
      popular: true
    },
    {
      icon: Plus,
      title: "Optional Add-ons",
      price: "Custom",
      period: "quoted per need",
      features: [
        "SEO meta tag optimization",
        "CMS or blog upgrade",
        "Analytics/reporting dashboard",
        "Weekly keyword focused articles"
      ],
      gradient: "bg-emerald-500",
      popular: false
    }
  ];

  return (
    <section id="services" className="py-20 bg-white dark:bg-slate-800">
      <div className="container mx-auto px-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-800 dark:text-slate-50">
            What We Offer
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300">
            Comprehensive website solutions designed for local Las Vegas businesses
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <motion.div
                key={service.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="relative"
              >
                {service.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-gradient-to-r from-blue-600 to-emerald-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}
                <Card className={`h-full hover:shadow-xl transition-all duration-300 ${
                  service.popular 
                    ? "bg-gradient-to-br from-blue-50 to-emerald-50 dark:from-slate-900 dark:to-slate-800 border-2 border-blue-200 dark:border-blue-500/30 hover:shadow-blue-200/50 dark:hover:shadow-blue-500/25" 
                    : "bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-700 hover:shadow-slate-200/50 dark:hover:shadow-slate-700/50"
                }`}>
                  <CardContent className="p-8">
                    <div className={`w-12 h-12 ${service.gradient} rounded-xl flex items-center justify-center mb-6`}>
                      <IconComponent className="text-white text-xl" />
                    </div>
                    <h3 className="text-2xl font-bold mb-4 text-slate-800 dark:text-slate-50">
                      {service.title}
                    </h3>
                    <ul className="space-y-3 text-slate-600 dark:text-slate-300 mb-6">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center">
                          <Check className="text-green-500 mr-3 h-4 w-4 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    <div className="pt-6 border-t border-slate-200 dark:border-slate-700">
                      <div className={`text-3xl font-bold ${
                        service.popular ? "text-emerald-600" : 
                        service.title === "Single Build" ? "text-blue-600" : "text-emerald-600"
                      }`}>
                        {service.price}
                      </div>
                      <div className="text-slate-500 dark:text-slate-400">
                        {service.period}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
