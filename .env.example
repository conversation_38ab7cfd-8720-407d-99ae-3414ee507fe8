# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Gmail SMTP Configuration for Email Notifications
# These are required for email functionality to work

# Gmail SMTP server settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false

# Your Gmail credentials
# Note: Use an App Password, not your regular Gmail password
# Generate an App Password at: https://myaccount.google.com/apppasswords
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email addresses
FROM_EMAIL=<EMAIL>
TO_EMAIL=<EMAIL>

# Instructions for Gmail Setup:
# 1. Enable 2-Factor Authentication on your Gmail account
# 2. Go to https://myaccount.google.com/apppasswords
# 3. Generate a new App Password for "Mail"
# 4. Use that App Password as SMTP_PASS (not your regular password)
# 5. Set SMTP_USER to your Gmail address
# 6. Set FROM_EMAIL to your Gmail address (must match SMTP_USER)
# 7. Set TO_EMAIL to where you want to receive lead notifications
