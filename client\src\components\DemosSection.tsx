import { ExternalLink } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";

export function DemosSection() {
  const demos = [
    {
      title: "Auto Detailing Site",
      description: "Complete business website with service gallery, booking system, and customer reviews",
      image: "https://images.unsplash.com/photo-*************-64acf2078ed9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600",
      url: "https://demo-01.sitenevada.com",
      alt: "Auto detailing business website demo showing professional car washing services"
    },
    {
      title: "Tax Services Site",
      description: "Professional accounting website with service details, appointment scheduling, and client portal",
      image: "https://images.unsplash.com/photo-**********-6726b3ff858f?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600",
      url: "https://demo-02.sitenevada.com",
      alt: "Tax services website demo showing professional accounting and tax preparation services"
    },
    {
      title: "Dentist Site",
      description: "Modern dental practice website with service information, online appointments, and patient resources",
      image: "https://images.unsplash.com/photo-*************-23dfddce3e95?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600",
      url: "https://demo-03.sitenevada.com",
      alt: "Dental practice website demo showing modern dental office and professional services"
    }
  ];

  return (
    <section id="demos" className="py-20 bg-white dark:bg-slate-800">
      <div className="container mx-auto px-6">
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center max-w-3xl mx-auto mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-slate-800 dark:text-slate-50">
            Live Demos
          </h2>
          <p className="text-xl text-slate-600 dark:text-slate-300">
            See our work in action - real websites for real businesses
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {demos.map((demo, index) => (
            <motion.div
              key={demo.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-700 hover:shadow-xl transition-all duration-300 group overflow-hidden">
                <div className="aspect-video bg-slate-200 dark:bg-slate-700 relative overflow-hidden">
                  <img 
                    src={demo.image} 
                    alt={demo.alt}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-colors duration-300"></div>
                  <div className="absolute top-4 right-4">
                    <Badge className="bg-green-500 text-white hover:bg-green-600">
                      Live
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold mb-2 text-slate-800 dark:text-slate-50">
                    {demo.title}
                  </h3>
                  <p className="text-slate-600 dark:text-slate-300 mb-4">
                    {demo.description}
                  </p>
                  <a 
                    href={demo.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-semibold transition-colors"
                  >
                    View Demo <ExternalLink className="ml-2 h-4 w-4" />
                  </a>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
